package se.firme.ms.usuario.rest.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import se.firme.commons.firmese.dto.ApiResponse;
import se.firme.ms.datos.models.dto.SolicitudFirmaUnificadaDTO;

/**
 * Cliente Feign para comunicación con ms-firma
 */
@FeignClient(name = "ms-firma")
public interface FirmaServiceClient {

    /**
     * Solicita firma unificada para autoregistro
     */
    @PostMapping(value = "/plantilla/v1/solicitar-firma", consumes = "application/json")
    ApiResponse solicitarFirmaUnificada(@RequestBody SolicitudFirmaUnificadaDTO request);
}
