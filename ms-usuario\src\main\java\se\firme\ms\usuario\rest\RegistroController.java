/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.usuario.rest;

import co.venko.ms.models.entity.AdmUsuario;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.ApiResponse;
import se.firme.commons.firmese.dto.DocumentoDTO;
import se.firme.commons.firmese.dto.RegistroDTO;
import se.firme.commons.firmese.dto.AutoregistroDTO;
import se.firme.commons.firmese.dto.ServicioDTO;
import se.firme.commons.models.projection.IUsuario;
import se.firme.commons.models.projection.IUsuarioOris;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.usuario.Utils.UsuarioHeaderUtil;
import se.firme.ms.usuario.negocio.UsuarioNegocio;
import se.firme.ms.usuario.rest.client.UsuarioVMSClient;

/**
 * @document RegistroController
 * <AUTHOR> Machado Jaimes
 * @fecha martes, agosto 18 de 2020, 12:19:06 PM
 */
@RestController
@RefreshScope
@RequestMapping("/usuario")
public class RegistroController {

    @Autowired
    private UsuarioNegocio usuarioNegocioService;

    @Autowired
    private UsuarioVMSClient usuarioVMSClient;
	@Autowired
	private HttpServletRequest request;

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> registrar(@RequestBody RegistroDTO datosRegistro) {
        try {
            usuarioNegocioService.procesoRegistro(datosRegistro);
            return new ResponseEntity<>(new ApiResponse.ok().data("Se ha enviado un EMail a la dirección de correo electrónico especificada para proceder con la confirmación del registro").build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * Endpoint específico para autoregistro con manejo inteligente de tokens expirados
     * Implementa el flujo completo incluyendo reenvíos de email
     */
    @PostMapping(path = "/autoregistro", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> autoregistro(@RequestBody AutoregistroDTO datosRegistro) {
        try {
            String mensaje = usuarioNegocioService.procesarAutoregistro(datosRegistro);
            return new ResponseEntity<>(new ApiResponse.ok().data(mensaje).build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }

    @PutMapping(path = "/completar-registro-documento", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> completar(@RequestBody DocumentoDTO documento) {
        try {
            usuarioNegocioService.completarRegistro(documento);
            return new ResponseEntity<>(new ApiResponse.ok().data("Registro exitoso").build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/guardar-usuario-adm", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> guardarUsuarioAdmi(@RequestBody AdmUsuario usuario) {
        try {
            usuarioVMSClient.guardarUsuarioAdm(usuario);
            return new ResponseEntity<>(new ApiResponse.ok().data("").build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }

     @PostMapping(path = "/servicio", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> obtenerServicio() {
    	Long idUsuario;
        try{
            idUsuario = UsuarioHeaderUtil.obtenerIdUsuario(request);
        } catch(IllegalArgumentException e) {
        	return UsuarioHeaderUtil.respuestaError(e.getMessage(), HttpStatus.UNAUTHORIZED);
        }
        
        if (usuarioNegocioService.getUsuarioService().findById(idUsuario) == null) {
			// Validar que el usuario existe en la base de datos
			return new ResponseEntity<>(new ApiResponse.error().data("Usuario no encontrado").build(), HttpStatus.NOT_FOUND);
		}
    	
        try {   
            return new ResponseEntity<>(new ApiResponse.ok()
                    .data(usuarioNegocioService.getUsuarioServicio(idUsuario))
                    .build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }


    @PostMapping(path = "/add-package", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> agregarPaquete(@RequestBody String usuario) {
        try {
            return new ResponseEntity<>(new ApiResponse.ok().data(usuarioNegocioService.agregarPaqueteServicio(usuario)).build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }
	
    @PostMapping(path = "/info/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> consultar(@PathVariable(name = "id") String id) {
    	 try {
    		IUsuario usuario= usuarioNegocioService.consultarUsuario(id);
            if(usuario!=null) {
            	 return new ResponseEntity<>(new ApiResponse.ok().data(usuario).build(), HttpStatus.OK);
            }
          
           
            return new ResponseEntity<>(new ApiResponse.ok().data("No hay registros ").build(), HttpStatus.OK);
         } catch (Exception e) {
             return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
         }
    }
    
    @PutMapping(path = "/servicio", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> actualizarServicio(@RequestBody ServicioDTO dto) {
        Long idUsuario;
        try {
            idUsuario = UsuarioHeaderUtil.obtenerIdUsuario(request);
        } catch (IllegalArgumentException e) {
            return UsuarioHeaderUtil.respuestaError(e.getMessage(), HttpStatus.UNAUTHORIZED);
        }
        if (usuarioNegocioService.getUsuarioService().findById(idUsuario) == null) {
            return UsuarioHeaderUtil.respuestaError("Usuario no encontrado", HttpStatus.NOT_FOUND);
        }
        // Sobrescribe el idServicio con el idUsuario extraído del header
        dto.setIdServicio(idUsuario);

        // lógica de negocio
        try {
            usuarioNegocioService.actualizarServicio(dto);
            return new ResponseEntity<>(new ApiResponse.ok()
                    .data("Datos de servicio actualizado correctamente")
                    .build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }
    
    @DeleteMapping(path = "/eliminar-cuenta", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> eliminarCuenta() {
        try {
        	
        	usuarioNegocioService.eliminarCuenta(Integer.parseInt(request.getHeader("X-USER-ID")));
            return new ResponseEntity<>(new ApiResponse.ok()
                    .data("Cuenta de usuario eliminada")
                    .build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping(path = "/buscar-documento-email/{docOrEmail}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> findByNumeroDeDocumentoOEmail(@PathVariable(name = "docOrEmail") String docOrEmail) {
        try {
            IUsuarioOris usuario = usuarioNegocioService.findByNumeroDeDocumentoOEmail(docOrEmail);

            if (usuario == null) {
                return new ResponseEntity<>(new ApiResponse.error().mensaje("Usuario no encontrado").build(), HttpStatus.NOT_FOUND);
            }

            return new ResponseEntity<>(new ApiResponse.ok().data(usuario).build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }
    
    @PutMapping(path = "/pwd", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<String> cambiarContrasena(@RequestBody String body) {
		try {
			usuarioNegocioService.cambiarContrasena(body);
			return new ResponseEntity<>("{\"mensaje: \"Contraseña cambiada\"}", HttpStatus.OK);
		} catch (Exception e) {
			return new ResponseEntity<>("{\"error\": \"" + e.getMessage() + "\"}", HttpStatus.BAD_REQUEST);
		}
	}

    @DeleteMapping(path = "v2/eliminar-cuenta/{idUsuario}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> eliminarCuenta(
            @PathVariable("idUsuario") int idUsuario,
            HttpServletRequest request) {
        
        // 1. VALIDACIONES DE ENTRADA - Extraer y validar X-USER-ID
        String xUserId = this.request.getHeader("X-USER-ID");
        
        if (xUserId == null || xUserId.trim().isEmpty()) {
            return new ResponseEntity<>(
                new ApiResponse.error().mensaje("Header X-USER-ID requerido").build(),
                HttpStatus.UNAUTHORIZED
            );
        }

        // Convertir ID de usuario referido del header
        Long idReferido;
        try {
            idReferido = Long.parseLong(xUserId.trim());
        } catch (NumberFormatException e) {
            return new ResponseEntity<>(
                new ApiResponse.error().mensaje("X-USER-ID debe ser un número válido").build(),
                HttpStatus.BAD_REQUEST
            );
        }
        
        // Validar que el ID de usuario referido es válido
        if (idReferido <= 0) {
            return new ResponseEntity<>(
                new ApiResponse.error().mensaje("X-USER-ID debe ser mayor a 0").build(),
                HttpStatus.BAD_REQUEST
            );
        }

        // 2. VALIDACIONES DE NEGOCIO
        try {
            // Verificar que el usuario a eliminar existe
            Usuario usuario = usuarioNegocioService.buscarUsuarioPorId(idUsuario);
            if (usuario == null) {
                return new ResponseEntity<>(new ApiResponse.error()
                    .mensaje("El usuario a eliminar no existe")
                    .build(), HttpStatus.NOT_FOUND);
            }

            // Validar autorización usando el método existente
            boolean autorizado = usuarioNegocioService.validarEliminacionUsuario(idUsuario, idReferido.intValue());
            if (!autorizado) {
                return new ResponseEntity<>(new ApiResponse.error()
                    .mensaje("No autorizado para eliminar este usuario")
                    .build(), HttpStatus.FORBIDDEN);
            }

            // 3. LÓGICA DE NEGOCIO - Eliminar la cuenta
            usuarioNegocioService.eliminarCuenta(idUsuario);
            
            return new ResponseEntity<>(new ApiResponse.ok()
                    .data("Cuenta de usuario eliminada")
                    .build(), HttpStatus.OK);
                    
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return new ResponseEntity<>(
                new ApiResponse.error().mensaje("Error interno del servidor").build(),
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

/*	
    @PostMapping(path = "listar/referidos", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> listarUsuariosPorReferido(HttpServletRequest request) {
        
        // 1. VALIDAR X-USER-ID
        String xUserId = this.request.getHeader("X-USER-ID");
        
        if (xUserId == null || xUserId.trim().isEmpty()) {
            return new ResponseEntity<>(
                new ApiResponse.error().mensaje("Header X-USER-ID requerido").build(),
                HttpStatus.UNAUTHORIZED
            );
        }

        Long idReferido;
        try {
            idReferido = Long.parseLong(xUserId.trim());
        } catch (NumberFormatException e) {
            return new ResponseEntity<>(
                new ApiResponse.error().mensaje("X-USER-ID debe ser un número válido").build(),
                HttpStatus.BAD_REQUEST
            );
        }
        
        if (idReferido <= 0) {
            return new ResponseEntity<>(
                new ApiResponse.error().mensaje("X-USER-ID debe ser mayor a 0").build(),
                HttpStatus.BAD_REQUEST
            );
        }

        try {
            // 2. OBTENER TODOS LOS USUARIOS REFERIDOS
            List<Usuario> todosLosUsuarios = usuarioNegocioService.listarUsuariosPorReferido(idReferido);
            
            if (todosLosUsuarios == null || todosLosUsuarios.isEmpty()) {
                return new ResponseEntity<>(
                    new ApiResponse.error().mensaje("No tiene usuarios referidos asociados").build(),
                    HttpStatus.NOT_FOUND
                );
            }
            
            // 3. MAPEAR A DTO
            List<UsuarioReferidoDTO> resultado = todosLosUsuarios.stream()
                .map(u -> new UsuarioReferidoDTO(
                    u.getIdUsuario(),
                    u.getNumeroDocumento(),
                    u.getNombreCompleto(),
                    u.getCorreoElectronico(),
                    u.getNumeroCelular(),
                    u.getActivo()
                ))
                .collect(Collectors.toList());
            
            Map<String, Object> respuesta = new HashMap<>();
            respuesta.put("usuarios", resultado);
            respuesta.put("totalUsuarios", resultado.size());
            
            return new ResponseEntity<>(new ApiResponse.ok().data(respuesta).build(), HttpStatus.OK);
            
        } catch (Exception e) {
            return new ResponseEntity<>(
                new ApiResponse.error().mensaje("Error interno del servidor").build(),
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    } 
*/
}
