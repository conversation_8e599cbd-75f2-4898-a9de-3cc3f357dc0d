package se.firme.ms.datos.models.dto;

import java.util.ArrayList;
import java.util.List;

public class SolicitudFirmaUnificadaDTO {
    
    // De un solo ID a una lista de plantillas
    
    // Lista de plantillas
    private List<PlantillaRequestDTO> plantillas;
    
    // PARA DOCUMENTOS NUEVOS
    private List<DocumentoNuevoDTO> documentos;
    
    // COMUNES
    private String fechaExpedicionDocumentoInteresado;
    private String tipoOrden = "PARALELO";
    private String fechaVigencia;
    private Long idUsuario;
    private String descripcionPersonalizada;
    private List<FirmanteOrdenDTO> firmantes;
    private String tipoFirma = "MULTIPLE";
    
    // Campos para usuarios no registrados
    private String nombreInteresado;
    private String emailInteresado;
    private String telefonoInteresado;
    private String tipoDocumentoInteresado;
    private String numeroDocumentoInteresado;
    private String claveInteresado="FIRMESE_";
    
    // Constructores
    public SolicitudFirmaUnificadaDTO() {}
    
    // Getters y Setters
    public List<PlantillaRequestDTO> getPlantillas() {
        return plantillas;
    }
    
    public void setPlantillas(List<PlantillaRequestDTO> plantillas) {
        this.plantillas = plantillas;
    }
    
    public List<DocumentoNuevoDTO> getDocumentos() { return documentos; }
    public void setDocumentos(List<DocumentoNuevoDTO> documentos) { this.documentos = documentos; }
    
    public String getTipoOrden() { return tipoOrden; }
    public void setTipoOrden(String tipoOrden) { this.tipoOrden = tipoOrden; }
    
    public String getFechaVigencia() { return fechaVigencia; }
    public void setFechaVigencia(String fechaVigencia) { this.fechaVigencia = fechaVigencia; }
    
    public Long getIdUsuario() { return idUsuario; }
    public void setIdUsuario(Long idUsuario) { this.idUsuario = idUsuario; }
    
    public String getDescripcionPersonalizada() { return descripcionPersonalizada; }
    public void setDescripcionPersonalizada(String descripcionPersonalizada) { this.descripcionPersonalizada = descripcionPersonalizada; }
    
    public List<FirmanteOrdenDTO> getFirmantes() { return firmantes; }
    public void setFirmantes(List<FirmanteOrdenDTO> firmantes) { this.firmantes = firmantes; }
    
    public String getTipoFirma() { return tipoFirma; }
    public void setTipoFirma(String tipoFirma) { this.tipoFirma = tipoFirma; }
    
    // Getters y setters para campos de interesado
    public String getNombreInteresado() { return nombreInteresado; }
    public void setNombreInteresado(String nombreInteresado) { this.nombreInteresado = nombreInteresado; }
    
    public String getEmailInteresado() { return emailInteresado; }
    public void setEmailInteresado(String emailInteresado) { this.emailInteresado = emailInteresado; }
    
    public String getTelefonoInteresado() { return telefonoInteresado; }
    public void setTelefonoInteresado(String telefonoInteresado) { this.telefonoInteresado = telefonoInteresado; }
    
    public String getTipoDocumentoInteresado() { return tipoDocumentoInteresado; }
    public void setTipoDocumentoInteresado(String tipoDocumentoInteresado) { this.tipoDocumentoInteresado = tipoDocumentoInteresado; }
    
    public String getNumeroDocumentoInteresado() { return numeroDocumentoInteresado; }
    public void setNumeroDocumentoInteresado(String numeroDocumentoInteresado) { this.numeroDocumentoInteresado = numeroDocumentoInteresado; }

    public String getFechaExpedicionDocumentoInteresado() {
    return fechaExpedicionDocumentoInteresado;
    }
    public void setFechaExpedicionDocumentoInteresado(String fechaExpedicionDocumentoInteresado) {
        this.fechaExpedicionDocumentoInteresado = fechaExpedicionDocumentoInteresado;
    }

    public String getClaveInteresado() {
        return claveInteresado;
    }

    public void setClaveInteresado(String claveInteresado) {
        this.claveInteresado = claveInteresado;
    }
    
    
    // Métodos de validación ACTUALIZADOS
    public boolean esPlantilla() {
        return plantillas != null && !plantillas.isEmpty();
    }
    
    public boolean esDocumentoNuevo() {
        return documentos != null && !documentos.isEmpty();
    }
    
    public boolean esSolicitudMixta() {
        return esPlantilla() && esDocumentoNuevo();
    }

    public int getTotalDocumentos() {
        int total = 0;
        if (esPlantilla()) total += plantillas.size(); // contar plantillas
        if (esDocumentoNuevo()) total += documentos.size();
        return total;
    }
    
    /**
     * Obtiene el primer ID de plantilla
     */
    public Long getIdPlantilla() {
        if (plantillas != null && !plantillas.isEmpty()) {
            return plantillas.get(0).getIdPlantilla();
        }
        return null;
    }
    
    /**
     * Método auxiliar para compatibilidad
     */
    public void setIdPlantilla(Long idPlantilla) {
        if (idPlantilla != null) {
            if (plantillas == null) {
                plantillas = new ArrayList<>();
            }
            plantillas.clear();
            PlantillaRequestDTO plantilla = new PlantillaRequestDTO();
            plantilla.setIdPlantilla(idPlantilla);
            plantillas.add(plantilla);
        }
    }
    
    public boolean esUsuarioRegistrado() {
        boolean resultado = idUsuario != null && idUsuario > 0;
        System.out.println("🔍 esUsuarioRegistrado() - idUsuario: " + idUsuario + " = " + resultado);
        return resultado;
    }
    
    public boolean esUsuarioNoRegistrado() {
        boolean tieneDatosCompletos = 
            (nombreInteresado != null && !nombreInteresado.trim().isEmpty()) &&
            (emailInteresado != null && !emailInteresado.trim().isEmpty()) &&
            (telefonoInteresado != null && !telefonoInteresado.trim().isEmpty()) &&
            (tipoDocumentoInteresado != null && !tipoDocumentoInteresado.trim().isEmpty()) &&
            (numeroDocumentoInteresado != null && !numeroDocumentoInteresado.trim().isEmpty());
        
        boolean noTieneIdUsuario = (idUsuario == null || idUsuario <= 0);
        
        boolean resultado = tieneDatosCompletos && noTieneIdUsuario;
        
        System.out.println("🔍 esUsuarioNoRegistrado():");
        System.out.println("   - Datos completos: " + tieneDatosCompletos);
        System.out.println("   - No tiene ID: " + noTieneIdUsuario);
        System.out.println("   - Resultado: " + resultado);
        
        return resultado;
    }

    /**
     * obtener tipo de usuario detectado
     */
    public String getTipoUsuarioDetectado() {
        boolean registrado = esUsuarioRegistrado();
        boolean noRegistrado = esUsuarioNoRegistrado();
        
        if (registrado && noRegistrado) {
            return "CONFLICTO_AMBOS";
        } else if (registrado) {
            return "REGISTRADO";
        } else if (noRegistrado) {
            return "NO_REGISTRADO"; 
        } else {
            return "INDEFINIDO";
        }
    }

    @Override
    public String toString() {
        return "SolicitudFirmaUnificadaDTO{" +
                "plantillas=" + (plantillas != null ? plantillas.size() : 0) + " plantilla(s)" +
                ", documentos=" + (documentos != null ? documentos.size() : 0) + " documento(s)" +
                ", tipoOrden='" + tipoOrden + '\'' +
                ", idUsuario=" + idUsuario +
                ", firmantes=" + (firmantes != null ? firmantes.size() : 0) + " firmante(s)" +
                ", esMixta=" + esSolicitudMixta() +
                ", totalDocumentos=" + getTotalDocumentos() +
                ", tipoUsuario=" + getTipoUsuarioDetectado() +
                '}';
    }
}