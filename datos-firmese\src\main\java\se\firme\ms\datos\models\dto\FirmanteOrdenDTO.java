package se.firme.ms.datos.models.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

public class FirmanteOrdenDTO {
    
    @JsonProperty("tipoDocumento")
    private String tipoDocumento;

    @JsonProperty("fechaExpedicionDocumento")

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private String fechaExpedicionDocumento;
    
    @JsonProperty("numeroDocumento")
    private String numeroDocumento;
    
    @JsonProperty("nombreCompleto")
    private String nombreCompleto;
    
    @JsonProperty("email")
    private String email;
    
    @JsonProperty("numeroCelular")
    private String numeroCelular;
    
    @JsonProperty("rol")
    private String rol = "Firmante"; // VALOR POR DEFECTO
    
    @JsonProperty("orden")
    private int orden = 1; // VALOR POR DEFECTO
    
    @JsonProperty("esUsuarioRegistrado")
    private Boolean esUsuarioRegistrado;

    @JsonProperty("clave")
    private String clave="FIRMESE_";
    
    // Getters y Setters
    public String getTipoDocumento() {
        return tipoDocumento;
    }
    
    public void setTipoDocumento(String tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }
    
    public String getNumeroDocumento() {
        return numeroDocumento;
    }
    
    public void setNumeroDocumento(String numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }
    
    public String getNombreCompleto() {
        return nombreCompleto;
    }
    
    public void setNombreCompleto(String nombreCompleto) {
        this.nombreCompleto = nombreCompleto;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getNumeroCelular() {
        return numeroCelular;
    }
    
    public void setNumeroCelular(String numeroCelular) {
        this.numeroCelular = numeroCelular;
    }
    
    public String getRol() {
        return rol != null && !rol.trim().isEmpty() ? rol : "Firmante";
    }
    
    public void setRol(String rol) {
        this.rol = (rol != null && !rol.trim().isEmpty()) ? rol : "Firmante";
    }
    
    public int getOrden() {
        return orden > 0 ? orden : 1;
    }
    
    public void setOrden(int orden) {
        this.orden = orden > 0 ? orden : 1;
    }

    public Boolean getEsUsuarioRegistrado() {
        return esUsuarioRegistrado;
    }

    public void setEsUsuarioRegistrado(Boolean esUsuarioRegistrado) {
        this.esUsuarioRegistrado = esUsuarioRegistrado;
    }

    public String getFechaExpedicionDocumento() {
    return fechaExpedicionDocumento;
    }
    public void setFechaExpedicionDocumento(String fechaExpedicionDocumento) {
        this.fechaExpedicionDocumento = fechaExpedicionDocumento;
    }

    public String getClave() {
        return clave;
    }

    public void setClave(String clave) {
        this.clave = clave;
    }

    
}