package se.firme.commons.firmese.dto;

import java.io.Serializable;

/**
 * DTO específico para autoregistro con todos los campos necesarios
 */
public class AutoregistroDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String tipoDocumento;
    private String numeroDocumento;
    private String fechaExpedicion;
    private String correoElectronico;
    private String numeroCelular;
    private String contrasena;
    private String nombreCompleto;

    public String getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(String tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    public String getNumeroDocumento() {
        return numeroDocumento;
    }

    public void setNumeroDocumento(String numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

    public String getFechaExpedicion() {
        return fechaExpedicion;
    }

    public void setFechaExpedicion(String fechaExpedicion) {
        this.fechaExpedicion = fechaExpedicion;
    }

    public String getCorreoElectronico() {
        return correoElectronico;
    }

    public void setCorreoElectronico(String correoElectronico) {
        this.correoElectronico = correoElectronico;
    }

    public String getNumeroCelular() {
        return numeroCelular;
    }

    public void setNumeroCelular(String numeroCelular) {
        this.numeroCelular = numeroCelular;
    }

    public String getContrasena() {
        return contrasena;
    }

    public void setContrasena(String contrasena) {
        this.contrasena = contrasena;
    }

    public String getNombreCompleto() {
        return nombreCompleto;
    }

    public void setNombreCompleto(String nombreCompleto) {
        this.nombreCompleto = nombreCompleto;
    }

    @Override
    public String toString() {
        return "AutoregistroDTO{" +
                "tipoDocumento='" + tipoDocumento + '\'' +
                ", numeroDocumento='" + numeroDocumento + '\'' +
                ", fechaExpedicion='" + fechaExpedicion + '\'' +
                ", correoElectronico='" + correoElectronico + '\'' +
                ", numeroCelular='" + numeroCelular + '\'' +
                ", nombreCompleto='" + nombreCompleto + '\'' +
                '}';
    }
}
