/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import org.hibernate.annotations.CreationTimestamp;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "usuario")
@XmlRootElement
@NamedQueries({
        @NamedQuery(name = "Usuario.findAll", query = "SELECT u FROM Usuario u")
        , @NamedQuery(name = "Usuario.findByIdUsuario", query = "SELECT u FROM Usuario u WHERE u.idUsuario = :idUsuario")
        , @NamedQuery(name = "Usuario.findByNumeroDocumento", query = "SELECT u FROM Usuario u WHERE u.numeroDocumento = :numeroDocumento")
        , @NamedQuery(name = "Usuario.findByFechaExpedicionDocumento", query = "SELECT u FROM Usuario u WHERE u.fechaExpedicionDocumento = :fechaExpedicionDocumento")
        , @NamedQuery(name = "Usuario.findByCorreoElectronico", query = "SELECT u FROM Usuario u WHERE u.correoElectronico = :correoElectronico")
        , @NamedQuery(name = "Usuario.findByNumeroCelular", query = "SELECT u FROM Usuario u WHERE u.numeroCelular = :numeroCelular")
        , @NamedQuery(name = "Usuario.findByNombreCompleto", query = "SELECT u FROM Usuario u WHERE u.nombreCompleto = :nombreCompleto")
        , @NamedQuery(name = "Usuario.findByProcesoRegistro", query = "SELECT u FROM Usuario u WHERE u.procesoRegistro = :procesoRegistro")
        , @NamedQuery(name = "Usuario.findByActivo", query = "SELECT u FROM Usuario u WHERE u.activo = :activo")
        , @NamedQuery(name = "Usuario.findByFechaRegistro", query = "SELECT u FROM Usuario u WHERE u.fechaRegistro = :fechaRegistro")})
@JsonIgnoreProperties(ignoreUnknown = true)
public class Usuario implements Serializable {

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "idUsuario", fetch = FetchType.LAZY)
    private List<ProcesoFirma> procesoFirmaList;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id_usuario")
    private Long idUsuario;
    @Basic(optional = false)
    @Lob
    @Column(name = "clave")
    private String clave;
    @Basic(optional = false)
    @Column(name = "numero_documento")
    private String numeroDocumento;
    @Basic(optional = false)
    @Column(name = "fecha_expedicion_documento")
    @Temporal(TemporalType.DATE)
    private Date fechaExpedicionDocumento;
    @Basic(optional = false)
    @Column(name = "correo_electronico")
    private String correoElectronico;
    @Basic(optional = false)
    @Column(name = "numero_celular")
    private String numeroCelular;
    @Lob
    @Column(name = "row_data")
    private String rowData;
    @Basic(optional = false)
    @Column(name = "nombre_completo")
    private String nombreCompleto;
    @Basic(optional = false)
    @Column(name = "proceso_registro")
    private boolean procesoRegistro;
    @Basic(optional = false)
    @Column(name = "activo")
    private boolean activo;
    @Basic(optional = false)
    @Column(name = "fecha_registro")
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    private Date fechaRegistro;
    @JoinColumn(name = "id_tipo_documento", referencedColumnName = "id_tipo_documento")
    @ManyToOne(optional = false)
    @JsonManagedReference
    private TipoDocumento idTipoDocumento;

    @Lob
    @Column(name = "documento_persona")
    private String documentoPersona;

    @JsonBackReference
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "idUsuario")
    private List<BitacoraRegistro> bitacoraRegistroList;

    @JsonBackReference
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "idUsuario")
    private List<FirmaArchivoUsuario> firmaArchivoUsuarioList;

    @JsonBackReference
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "idUsuario")
    private List<Token> tokenList;

    @Basic(optional = false)
    @Column(name = "verificado_fuente")
    private boolean verificadoFuente;
    @Column(name = "fecha_verificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaVerificacion;
    @Column(name = "observacion_verificacion")
    private String obervacionVerificacion;
    @Column(name = "estado")
    private boolean estado;
    @Column(name = "id_referido")
    private Long idReferido;

    @Basic(optional = false)
    @Column(name = "firmado_tyc")
    private boolean firmadoTyc = false; // Por defecto false (0)

    @Basic(optional = false)
    @Column(name = "tipo_registro")
    private Integer tipoRegistro;




    public Usuario() {
    }

    public Usuario(Long idUsuario) {
        this.idUsuario = idUsuario;
    }

    public Usuario(Long idUsuario, String clave, String numeroDocumento, Date fechaExpedicionDocumento, String correoElectronico, String numeroCelular, String nombreCompleto, boolean procesoRegistro, boolean activo, Date fechaRegistro) {
        this.idUsuario = idUsuario;
        this.clave = clave;
        this.numeroDocumento = numeroDocumento;
        this.fechaExpedicionDocumento = fechaExpedicionDocumento;
        this.correoElectronico = correoElectronico.toLowerCase();
        this.numeroCelular = numeroCelular;
        this.nombreCompleto = nombreCompleto;
        this.procesoRegistro = procesoRegistro;
        this.activo = activo;
        this.fechaRegistro = fechaRegistro;
    }

    public Long getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(Long idUsuario) {
        this.idUsuario = idUsuario;
    }

    public String getClave() {
        return clave;
    }

    public void setClave(String clave) {
        this.clave = clave;
    }

    public String getNumeroDocumento() {
        return numeroDocumento;
    }

    public void setNumeroDocumento(String numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

    public Date getFechaExpedicionDocumento() {
        return fechaExpedicionDocumento;
    }

    public void setFechaExpedicionDocumento(Date fechaExpedicionDocumento) {
        this.fechaExpedicionDocumento = fechaExpedicionDocumento;
    }

    public String getCorreoElectronico() {
        return correoElectronico;
    }

    public void setCorreoElectronico(String correoElectronico) {
        if (correoElectronico != null) {
            correoElectronico = correoElectronico.toLowerCase();
        }

        this.correoElectronico = correoElectronico;
    }

    public String getNumeroCelular() {
        return numeroCelular;
    }

    public void setNumeroCelular(String numeroCelular) {
        this.numeroCelular = numeroCelular;
    }

    public String getRowData() {
        return rowData;
    }

    public void setRowData(String rowData) {
        this.rowData = rowData;
    }

    public String getNombreCompleto() {
        return nombreCompleto;
    }

    public void setNombreCompleto(String nombreCompleto) {
        this.nombreCompleto = nombreCompleto;
    }

    public boolean getProcesoRegistro() {
        return procesoRegistro;
    }

    public void setProcesoRegistro(boolean procesoRegistro) {
        this.procesoRegistro = procesoRegistro;
    }

    public boolean getActivo() {
        return activo;
    }

    public void setActivo(boolean activo) {
        this.activo = activo;
    }

    public Date getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(Date fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    public TipoDocumento getIdTipoDocumento() {
        return idTipoDocumento;
    }

    public void setIdTipoDocumento(TipoDocumento idTipoDocumento) {
        this.idTipoDocumento = idTipoDocumento;
    }

    @XmlTransient
    public List<BitacoraRegistro> getBitacoraRegistroList() {
        return bitacoraRegistroList;
    }

    public void setBitacoraRegistroList(List<BitacoraRegistro> bitacoraRegistroList) {
        this.bitacoraRegistroList = bitacoraRegistroList;
    }

    @XmlTransient
    public List<FirmaArchivoUsuario> getFirmaArchivoUsuarioList() {
        return firmaArchivoUsuarioList;
    }

    public void setFirmaArchivoUsuarioList(List<FirmaArchivoUsuario> firmaArchivoUsuarioList) {
        this.firmaArchivoUsuarioList = firmaArchivoUsuarioList;
    }

    @XmlTransient
    public List<Token> getTokenList() {
        return tokenList;
    }

    public void setTokenList(List<Token> tokenList) {
        this.tokenList = tokenList;
    }

    public boolean isEstado() {
        return estado;
    }

    public void setEstado(boolean estado) {
        this.estado = estado;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (idUsuario != null ? idUsuario.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof Usuario)) {
            return false;
        }
        Usuario other = (Usuario) object;
        if ((this.idUsuario == null && other.idUsuario != null) || (this.idUsuario != null && !this.idUsuario.equals(other.idUsuario))) {
            return false;
        }
        return true;
    }


    @Override
    public String toString() {
        return "Usuario [idUsuario=" + idUsuario + ", numeroDocumento=" + numeroDocumento + ", nombreCompleto="
                + nombreCompleto + ", activo=" + activo + "]";
    }

    public boolean getVerificadoFuente() {
        return verificadoFuente;
    }

    public void setVerificadoFuente(boolean verificadoFuente) {
        this.verificadoFuente = verificadoFuente;
    }

    public Date getFechaVerificacion() {
        return fechaVerificacion;
    }

    public void setFechaVerificacion(Date fechaVerificacion) {
        this.fechaVerificacion = fechaVerificacion;
    }

    public String getObervacionVerificacion() {
        return obervacionVerificacion;
    }

    public void setObervacionVerificacion(String obervacionVerificacion) {
        this.obervacionVerificacion = obervacionVerificacion;
    }

    public String getDocumentoPersona() {
        return documentoPersona;
    }

    public void setDocumentoPersona(String documentoPersona) {
        this.documentoPersona = documentoPersona;
    }

    @XmlTransient
    public List<ProcesoFirma> getProcesoFirmaList() {
        return procesoFirmaList;
    }

    public void setProcesoFirmaList(List<ProcesoFirma> procesoFirmaList) {
        this.procesoFirmaList = procesoFirmaList;
    }

    public long getIdReferido() {
        return idReferido;
    }

    public void setIdReferido(long idReferido) {
        this.idReferido = idReferido;
    }

    public boolean getFirmadoTyc() {
        return firmadoTyc;
    }

    public void setFirmadoTyc(boolean firmadoTyc) {
        this.firmadoTyc = firmadoTyc;
    }

    public boolean haFirmadoTerminosCondiciones() {
        return firmadoTyc;
    }

    public void marcarTerminosCondicionesFirmados() {
        this.firmadoTyc = true;
    }

    public Integer getTipoRegistro() {
        return tipoRegistro;
    }

    public void setTipoRegistro(Integer tipoRegistro) {
        this.tipoRegistro = tipoRegistro;
    }

    
}
