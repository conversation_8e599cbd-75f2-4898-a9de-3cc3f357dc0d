/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.service;

import java.util.logging.Level;
import java.util.logging.Logger;
import se.firme.commons.firmese.enumerados.EParametro;
import se.firme.commons.firmese.util.Parameters;



/**
 * @document EmailTemplateService
 * <AUTHOR>
 * @fecha viernes, agosto 21 de 2020, 04:08:17 PM
 */
public class EmailTemplateService {
	/**
	 * Envío de correo para completar registro de usuario
	 * 
	 * @param token       Token de validación de correo
	 * @param urlFrontend url del front en el ambiente que está corriendo la
	 *                    aplicación
	 * @return
	 */
	public static String registrationEmailTemplate(String token, String urlFrontend) {
		StringBuilder builder = new StringBuilder();
		builder.append("<div>");
		builder.append("<h2>Estimado usuario</h2>");
		builder.append("<p>¡Felicitaciones, hemos recibido tu solicitud de registro en <b>Fírme.se</b>!.</p>");
		builder.append("<p>Para verificar el correo electrónico por favor haz clic en el enlace a continuación: ");

		builder.append("<a href='");
		builder.append(urlFrontend);
		builder.append("/validar?tkn=");
		builder.append(token);
		builder.append("' target='_blank'>clic aquí</a></p>");
		builder.append(
				"<p>Si tienes problemas para abrir el enlace, por favor copia y pega la url a continuación en el navegador de tu preferencia</p>");
		builder.append("<code>");
		builder.append("<a href='");
		builder.append(urlFrontend);
		builder.append("/validar?tkn=");
		builder.append(token);
		builder.append("'>");
		builder.append(urlFrontend);
		builder.append("/validar?tkn=");
		builder.append(token);
		builder.append("</a>");
		builder.append("</code>");
		builder.append("<br/>");
		builder.append(
				"<p>Recuerda seguir las instrucciones del enlace para que puedas completar el registro de usuario.</p>");
		builder.append("<br/>");
		builder.append("<p>Saludos cordiales.</p>");
		builder.append("</div>");

		return builder.toString();
	}

	public static String notificacionFirmasEmailTemplate() {
		StringBuilder builder = new StringBuilder();
		builder.append("<div>");
		builder.append("<h2>Estimado usuario</h2>");
		builder.append("<p>Esta es una notificación de firma de documentos en la plataforma <b>Fírmese</b>.</p>");
		builder.append("<p>A continuación se anexan los documentos firmados exitosamente.</p>");

		return builder.toString();
	}
	
	

	public static String contenidoEnvioOTP(String codigo) {
		StringBuilder builder = new StringBuilder();
		builder.append("<div>");
		builder.append("<h2>Estimado usuario</h2>");
		builder.append("<p>A continuación indicamos tu código de verificación para firma de documentos en la plataforma <b>Fírmese</b>.</p>");
		builder.append("<h4>");
		builder.append(codigo);
		builder.append("</h4>");
		builder.append("<p>Saludos cordiales.</p>");

		return builder.toString();
	}
	
	public static String cambioContrasenaTemplate(String token, String urlFrontend) {
		StringBuilder builder = new StringBuilder();
		builder.append("<div>");
		builder.append("<h2>Estimado usuario</h2>");
		builder.append("<p>Hemos recibido una solicitud para cambio de contraseña <b>Fírme.se</b>!.</p>");
		builder.append("<p>Para continuar con el proceso por favor haz clic en el enlace a continuación: ");

		builder.append("<a href='");
		builder.append(urlFrontend);
		builder.append("/change-passwd/");
		builder.append(token);
		builder.append("' target='_blank'>clic aquí</a></p>");
		builder.append(
				"<p>Si tienes problemas para abrir el enlace, por favor copia y pega la url a continuación en el navegador de tu preferencia</p>");
		builder.append("<code>");
		builder.append("<a href='");
		builder.append(urlFrontend);
		builder.append("/change-passwd/");
		builder.append(token);
		builder.append("'>");
		builder.append(urlFrontend);
		builder.append("/change-passwd/");
		builder.append(token);
		builder.append("</a>");
		builder.append("</code>");
		builder.append("<br/>");

		builder.append("<p>Saludos cordiales.</p>");
		builder.append("</div>");

		return builder.toString();
	}

	public static String getTemplateFirmaMultiple(String token, String urlFrontend) {
		StringBuilder builder = new StringBuilder();
		builder.append("<div>");
		builder.append("<h2>Estimado usuario</h2>");
		builder.append("<p>Tienes una solicitud de firma <b>F&iacute;rme.se</b>!.</p>");
		builder.append("<p>Para continuar con el proceso por favor haz clic en el enlace a continuaci&oacute;n: ");

		builder.append("<a href='");
		builder.append(urlFrontend);
		builder.append("/multiple-firma/");
		builder.append(token);
		builder.append("' target='_blank'>clic aqu&iacute;</a></p>");
		builder.append(
				"<p>Si tienes problemas para abrir el enlace, por favor copia y pega la url a continuaci&oacute;n en el navegador de tu preferencia</p>");
		builder.append("<code>");
		builder.append("<a href='");
		builder.append(urlFrontend);
		builder.append("/multiple-firma/");
		builder.append(token);
		builder.append("'>");
		builder.append(urlFrontend);
		builder.append("/multiple-firma/");
		builder.append(token);
		builder.append("</a>");
		builder.append("</code>");
		builder.append("<br/>");

		builder.append("<p>Saludos cordiales.</p>");
		builder.append("</div>");

		return builder.toString();
	}

	/**
	 * Plantilla específica para finalización de autoregistro (firma de documentos fundacionales)
	 * @param token Token de validación
	 * @param urlFrontend URL del frontend
	 * @return HTML de la plantilla para finalización de autoregistro
	 */
	public static String getTemplateFinalizacionAutoregistro(String token, String urlFrontend) {
		StringBuilder builder = new StringBuilder();
		builder.append("<div>");
		builder.append("<h2>¡Bienvenido a F&iacute;rmese!</h2>");
		builder.append("<p>Estamos muy contentos de que se una a nuestra comunidad. Ha iniciado su proceso de registro y solo falta un &uacute;ltimo paso para activar su cuenta y empezar a firmar electr&oacute;nicamente.</p>");

		builder.append("<p>Para garantizar la seguridad y la validez legal de sus futuras firmas, es necesario que revise y acepte nuestros documentos fundacionales: los <b>T&eacute;rminos y Condiciones del Servicio</b> y la <b>Autorizaci&oacute;n para el Tratamiento de Datos Personales de VENLEG S.A.S.</b></p>");

		builder.append("<p>La firma de estos documentos finalizar&aacute; su proceso de registro.</p>");

		builder.append("<p>Por favor, haga clic en el siguiente bot&oacute;n para ser dirigido a nuestra plataforma segura, donde podr&aacute; revisar y firmar los documentos.</p>");

		// Botón principal
		builder.append("<div style='text-align: center; margin: 20px 0;'>");
		builder.append("<a href='");
		builder.append(urlFrontend);
		builder.append("/multiple-firma/");
		builder.append(token);
		builder.append("' target='_blank' style='background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;'>Finalizar Registro y Firmar Documentos</a>");
		builder.append("</div>");

		builder.append("<p>Si el bot&oacute;n anterior no funciona, puede copiar y pegar el siguiente enlace en su navegador:</p>");
		builder.append("<code>");
		builder.append("<a href='");
		builder.append(urlFrontend);
		builder.append("/multiple-firma/");
		builder.append(token);
		builder.append("'>");
		builder.append(urlFrontend);
		builder.append("/multiple-firma/");
		builder.append(token);
		builder.append("</a>");
		builder.append("</code>");
		builder.append("<br/><br/>");

		builder.append("<p>Gracias por su confianza.</p>");
		builder.append("<p>Atentamente,<br/>El equipo de F&iacute;rmese</p>");
		builder.append("</div>");

		return builder.toString();
	}

	/**
	 * Método para obtener la plantilla correcta en caso de que sea autoregistro
	 * @param token Token de validación
	 * @param urlFrontend URL del frontend
	 * @param esAutoregistro true si es proceso de autoregistro, false para firma normal
	 * @return HTML de la plantilla correspondiente
	 */
	public static String getTemplateSegunContexto(String token, String urlFrontend, boolean esAutoregistro) {
		if (esAutoregistro) {
			return getTemplateFinalizacionAutoregistro(token, urlFrontend);
		} else {
			return getTemplateFirmaMultiple(token, urlFrontend);
		}
	}

}
